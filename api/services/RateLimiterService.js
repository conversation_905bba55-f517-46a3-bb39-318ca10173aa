const Redis = require('ioredis');
const { RateLimiterRedis } = require('rate-limiter-flexible');

class RateLimiterService {
    constructor() {
        this.client = null;
        this.initialized = false;
        this.shutdownRegistered = false;
    }

    /**
     * Initialize the singleton ioredis client
     * @param {Object} options - Optional configuration options
     */
    init(options = {}) {
        if (this.initialized) {
            return Promise.resolve();
        }

        try {
            // Get Redis connection configuration
            const redisConfig = sails.config.connections.redis;

            // Create ioredis client with fast-fail options
            const clientOptions = {
                maxRetriesPerRequest: 1,
                enableOfflineQueue: false,
                connectTimeout: 800,
                lazyConnect: true,
                keyPrefix: 'sw:rl:vs:',
                ...options
            };

            // Handle different Redis connection formats
            if (typeof redisConfig === 'string') {
                this.client = new Redis(redisConfig, clientOptions);
            } else {
                this.client = new Redis({
                    ...redisConfig,
                    ...clientOptions
                });
            }

            // Set up event handlers
            this.client.on('error', (err) => {
                sails.log.warn('RateLimiterService Redis error:', err.code || err.message);
            });

            this.client.on('ready', () => {
                sails.log.verbose('RateLimiterService Redis client ready');
            });

            this.initialized = true;

            // Register shutdown hook
            this._registerShutdownHook();

            return Promise.resolve();
        } catch (error) {
            sails.log.error('RateLimiterService initialization failed:', error);
            return Promise.reject(error);
        }
    }

    /**
     * Register graceful shutdown hook
     */
    _registerShutdownHook() {
        if (this.shutdownRegistered) {
            return;
        }

        // Register with Sails lowering event
        sails.on('lowering', () => {
            this._gracefulShutdown();
        });

        this.shutdownRegistered = true;
    }

    /**
     * Gracefully close the Redis client
     */
    async _gracefulShutdown() {
        if (!this.client) {
            return;
        }

        try {
            sails.log.verbose('RateLimiterService: Gracefully shutting down Redis client');

            // Try to quit gracefully with a timeout
            const quitPromise = this.client.quit();
            const timeoutPromise = new Promise((resolve) => {
                setTimeout(() => resolve('timeout'), 2000);
            });

            const result = await Promise.race([quitPromise, timeoutPromise]);

            if (result === 'timeout') {
                sails.log.warn('RateLimiterService: Quit timeout, forcing disconnect');
                this.client.disconnect();
            }
        } catch (error) {
            sails.log.warn('RateLimiterService: Error during shutdown, forcing disconnect:', error.message);
            this.client.disconnect();
        }
    }

    /**
     * Soft timeout wrapper for limiter operations
     * @param {Promise} promise - The promise to wrap
     * @param {number} timeoutMs - Timeout in milliseconds
     */
    _withSoftTimeout(promise, timeoutMs) {
        const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => resolve({ timeout: true }), timeoutMs);
        });

        return Promise.race([promise, timeoutPromise]);
    }

    /**
     * Create rate limiter middleware
     * @param {string} rateLimiterName - Name of the rate limiter (e.g., 'read', 'write', 'api-v1')
     * @param {Object} config - Rate limiter configuration { points, duration, blockDuration, enableSoftTimeout, softTimeoutMs }
     * @param {Function} getKeySelector - Function to extract client ID from request
     * @returns {Function} Express/Sails middleware function
     */
    createRateLimiter(rateLimiterName, config, getKeySelector) {
        // Lazy initialization
        if (!this.initialized) {
            this.init();
        }

        // Create RateLimiterRedis instance for this rate limiter
        const limiter = new RateLimiterRedis({
            storeClient: this.client,
            keyPrefix: `${rateLimiterName}:`,
            points: config.points,
            duration: config.duration,
            blockDuration: config.blockDuration || 0,
            execEvenly: true
        });

        const enableSoftTimeout = config.enableSoftTimeout !== false;
        const softTimeoutMs = config.softTimeoutMs || 75;

        // Return middleware function
        return async (req, res, next) => {
            try {
                if (req.method === 'OPTIONS') {
                    return next(); // Skip OPTIONS requests
                }

                const keySelector = await getKeySelector(req);
                if (!keySelector) {
                    // Fail-open if no key selector
                    sails.log.warn('RateLimiterService: No key selector, failing open');
                    return next();
                }

                // Consume 1 point with an optional soft timeout
                const result = await (enableSoftTimeout
                    ? this._withSoftTimeout(limiter.consume(keySelector), softTimeoutMs)
                    : limiter.consume(keySelector)
                );

                // Handle soft timeout
                if (result && result.timeout) {
                    sails.log.warn('RateLimiterService soft timeout:', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: 'soft_timeout'
                    });
                    // Fail-open on timeout
                    return next();
                }

                // Success - set rate limit headers
                const remainingPoints = result.remainingPoints || 0;
                const msBeforeNext = result.msBeforeNext || 0;

                res.set({
                    'RateLimit-Limit': config.points,
                    'RateLimit-Remaining': remainingPoints,
                    'RateLimit-Reset': Math.ceil(msBeforeNext / 1000)
                });

                next();

            } catch (error) {
                const keySelector = await getKeySelector(req);

                if (error.remainingPoints !== undefined) {
                    // Rate limit exceeded
                    const msBeforeNext = error.msBeforeNext || 0;
                    const retryAfterSeconds = Math.ceil(msBeforeNext / 1000);

                    // Set headers
                    res.set({
                        'RateLimit-Limit': config.points,
                        'RateLimit-Remaining': 0,
                        'RateLimit-Reset': retryAfterSeconds,
                        'Retry-After': retryAfterSeconds
                    });

                    // Log rate limit exceeded
                    sails.log.info('Rate limit exceeded:', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        method: req.method,
                        path: req.path,
                        retryAfterMs: msBeforeNext
                    });

                    // Log block duration if configured
                    if (config.blockDuration > 0) {
                        sails.log.warn('Rate limit block applied:', {
                            rateLimiter: rateLimiterName,
                            keySelector,
                            path: req.path,
                            blockDuration: config.blockDuration
                        });
                    }

                    // Return 429 response
                    return res.status(429).json({
                        error: 'rate_limited',
                        rateLimiter: rateLimiterName,
                        retryAfterMs: msBeforeNext
                    });
                } else {
                    // Redis error or other error - fail open
                    sails.log.warn('RateLimiterService error (failing open):', {
                        rateLimiter: rateLimiterName,
                        keySelector,
                        path: req.path,
                        error: error.code || error.message
                    });

                    // Fail-open: don't set rate-limit headers, just continue
                    next();
                }
            }
        };
    }
}

module.exports = new RateLimiterService();
